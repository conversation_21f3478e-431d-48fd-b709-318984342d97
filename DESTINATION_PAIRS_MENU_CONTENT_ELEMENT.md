# Destination Pairs Menu Content Element

## Overview

The **Destination Pairs Menu** is a custom content element for the Flight Landing Pages extension that displays a dynamic menu of available flight destinations. This content element is specifically designed to work with Flight Landing Pages (doktype 201) and provides an interactive way for users to browse available flight routes.

## Features

- **Dynamic Content**: Automatically displays available destination pairs from the flight routes database
- **Page Type Restriction**: Only available on Flight Landing Pages (doktype 201) 
- **Header Support**: Includes standard TYPO3 header functionality with placeholder support
- **Backend Preview**: Shows helpful information in the backend about available destinations
- **Custom Icon**: Uses a distinctive airplane menu icon for easy identification

## Usage

### Adding the Content Element

1. Navigate to a Flight Landing Page (doktype 201)
2. Add a new content element
3. In the content element wizard, look for the "Flight Landing Pages" group
4. Select "Destination Pairs Menu"

### Configuration Options

The content element includes the same configuration options as the standard Header content element:

#### General Tab
- **Header**: Optional header text that will be displayed above the menu
  - Supports placeholders like `[_origin_code_]` and `[_destination_name_]` for dynamic content
- **Header Type**: Choose from H1-H6 or other header types
- **Header Link**: Optional link for the header

#### Appearance Tab
- **Frame**: Choose frame/wrapper options
- **Space Before/After**: Control spacing around the element

#### Access Tab
- **Hidden**: Hide/show the element
- **Start/End Time**: Schedule when the element should be visible

## Backend Preview

The content element provides a helpful backend preview that shows:

- ✓ Confirmation when placed on a Flight Landing Page
- ⚠ Warning when placed on incompatible page types
- Number of available destination pairs
- Warning if no destination pairs are configured

## Technical Details

### File Structure
```
Classes/
├── Preview/
│   └── DestinationPairsMenuPreviewRenderer.php
└── UserFunctions/
    └── ContentElementRestriction.php

Configuration/
├── TCA/Overrides/
│   └── tt_content.php
└── page.tsconfig

Resources/
├── Private/Language/
│   └── locallang_db.xlf
└── Public/Icons/
    └── content-destination-pairs-menu.svg
```

### TCA Configuration
- **CType**: `flightlandingpages_destinationpairsmenu`
- **Group**: `flight_landing_pages`
- **Icon**: `content-destination-pairs-menu`
- **Preview Renderer**: `DestinationPairsMenuPreviewRenderer`

### Page TSconfig Restrictions
The content element is automatically restricted to Flight Landing Pages through Page TSconfig:

```typoscript
[page["doktype"] == 201]
    # Enable on Landing Pages
    TCEFORM.tt_content.CType.addItems {
        flightlandingpages_destinationpairsmenu = ...
    }
[END]

[page["doktype"] != 201]
    # Hide on other page types
    TCEFORM.tt_content.CType.removeItems := addToList(flightlandingpages_destinationpairsmenu)
[END]
```

## Frontend Rendering

The frontend rendering will be implemented in a future update and will include:

- List view of available destinations
- Grid/card layout options
- Filtering capabilities
- Responsive design
- Integration with flight route data

## Troubleshooting

### Content Element Not Visible
- Ensure you're on a Flight Landing Page (doktype 201)
- Check that the extension is properly installed
- Clear TYPO3 caches

### No Destinations Showing
- Verify that flight route records are created for the page
- Check that routes are marked as active
- Ensure routes are not deleted or hidden

### Backend Preview Issues
- Clear backend caches
- Check database permissions
- Verify TCA configuration is loaded

## Best Practices

1. **Use Meaningful Headers**: Add descriptive headers to help users understand the menu purpose
2. **Configure Routes First**: Set up flight route records before adding the content element
3. **Test on Different Devices**: Ensure the menu works well on mobile and desktop
4. **Use Placeholders**: Take advantage of placeholder functionality in headers for dynamic content

## Related Documentation

- [Flight Landing Pages Extension Overview](README.md)
- [Virtual Route Integration](VIRTUAL_ROUTE_INTEGRATION.md)
- [Frontend Rendering Guide](FRONTEND_RENDERING_GUIDE.md)
- [Backend Preview Testing](BACKEND_PREVIEW_TESTING.md)
