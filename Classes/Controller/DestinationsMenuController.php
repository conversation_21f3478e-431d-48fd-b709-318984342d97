<?php
namespace Bgs\FlightLandingPages\Controller;

use Psr\Http\Message\ResponseInterface;
use TYPO3\CMS\Core\Site\Entity\Site;
use TYPO3\CMS\Extbase\Mvc\Controller\ActionController;
use Bgs\FlightLandingPages\Domain\Repository\FlightRouteRepository;

class DestinationsMenuController extends ActionController
{
    protected FlightRouteRepository $flightRouteRepository;

    public function injectFlightRouteRepository(FlightRouteRepository $flightRouteRepository): void
    {
        $this->flightRouteRepository = $flightRouteRepository;
    }

    /**
     * List all available flight destinations for the current site
     */
    public function listAction(): ResponseInterface
    {
        // Get current site identifier
        $site = $this->request->getAttribute('site');
        $siteIdentifier = $site instanceof Site ? $site->getIdentifier() : 'default';

        // Get display mode from FlexForm
        $displayMode = $this->settings['displayMode'] ?? 'list';
        $showOriginFilter = (bool)($this->settings['showOriginFilter'] ?? false);
        $showDestinationFilter = (bool)($this->settings['showDestinationFilter'] ?? false);

        // Get all active routes for this site
        $routes = $this->flightRouteRepository->findActiveBySite($siteIdentifier);

        // Get unique origins and destinations for filters
        $origins = [];
        $destinations = [];
        foreach ($routes as $route) {
            $origins[$route->getOriginCode()] = $route->getOriginName();
            $destinations[$route->getDestinationCode()] = $route->getDestinationName();
        }

        $this->view->assignMultiple([
            'routes' => $routes,
            'displayMode' => $displayMode,
            'showOriginFilter' => $showOriginFilter,
            'showDestinationFilter' => $showDestinationFilter,
            'origins' => $origins,
            'destinations' => $destinations,
            'siteIdentifier' => $siteIdentifier
        ]);

        return $this->htmlResponse();
    }
}
