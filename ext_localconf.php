<?php
defined('TYPO3') or die();

call_user_func(static function () {
    // Configure DestinationsMenu plugin (for content elements)
    \TYPO3\CMS\Extbase\Utility\ExtensionUtility::configurePlugin(
        'FlightLandingPages',
        'DestinationsMenu',
        [
            \Bgs\FlightLandingPages\Controller\DestinationsMenuController::class => 'list',
        ],
        // non-cacheable actions
        []
    );



    // Register page type icons
    $iconRegistry = \TYPO3\CMS\Core\Utility\GeneralUtility::makeInstance(\TYPO3\CMS\Core\Imaging\IconRegistry::class);
    $iconRegistry->registerIcon(
        'apps-pagetree-flight-template',
        \TYPO3\CMS\Core\Imaging\IconProvider\SvgIconProvider::class,
        ['source' => 'EXT:flight_landing_pages/Resources/Public/Icons/apps-pagetree-flight-template.svg']
    );
    $iconRegistry->registerIcon(
        'apps-pagetree-flight-landing',
        \TYPO3\CMS\Core\Imaging\IconProvider\SvgIconProvider::class,
        ['source' => 'EXT:flight_landing_pages/Resources/Public/Icons/apps-pagetree-flight-landing.svg']
    );
    $iconRegistry->registerIcon(
        'content-destination-pairs-menu',
        \TYPO3\CMS\Core\Imaging\IconProvider\SvgIconProvider::class,
        ['source' => 'EXT:flight_landing_pages/Resources/Public/Icons/content-destination-pairs-menu.svg']
    );

    // Register page TSconfig for TYPO3 v11 compatibility
    // In v12+ this is loaded automatically from Configuration/page.tsconfig
    $versionInformation = \TYPO3\CMS\Core\Utility\GeneralUtility::makeInstance(\TYPO3\CMS\Core\Information\Typo3Version::class);
    if ($versionInformation->getMajorVersion() < 12) {
        \TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addPageTSConfig(
            '@import "EXT:flight_landing_pages/Configuration/page.tsconfig"'
        );
    }

});
